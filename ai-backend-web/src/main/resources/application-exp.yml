#测试环境公共配置
---
spring:
  config:
    activate:
      on-profile: exp
  datasource:
    username: djai_backend_sR6s
    password: JIh$B04GvhQcq#em
    url: *********************************************************************************************************************


dataServer:
  app_key: c59IrXu7
  url: http://data-server/data-server
dify:
  url: http://172.31.0.16:32367
  smart_brief:
    url: http://172.31.0.16:32367
  key:
    run_sql: app-Z8zs7ES4Onfem4VBMimGx2jA
