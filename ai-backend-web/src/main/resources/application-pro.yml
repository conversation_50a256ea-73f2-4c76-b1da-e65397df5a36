
spring:
  config:
    activate:
      on-profile: pro
  datasource:
    username: djai_backend_m2N8W
    password: ker6<PERSON>ha#U6BL$h3e
    url: *********************************************************************************************************************


apollo:
  meta: http://service-apollo-config-server-dev.apollo.svc.cluster.local:8080
  bootstrap:
    enabled: true
    namespaces: application,url-conf,conf,DJCPS.commonu-utils


dataServer:
  app_key: c59IrXu7
  url: http://data-server/data-server
dify:
  url: http://dify-api.dify-update.svc.cluster.local:5001
  # 智能简报使用不同的dify实例
  smart_brief:
    url: http://dify-api.dify.svc.cluster.local:5001
  key:
    ai_report: app-lHHyfsjmsuX2JAgXvNE6E3kr
    agent: app-dp1kkdRl3ciALm9WOJiSbG1b
    manage_board: app-bHUo09OJph4PewZIT4lEqlZV
    faq: app-NJUdo8ZPTtv3SktM9rUAJn7i
    faq_easyorder: app-NJUdo8ZPTtv3SktM9rUAJn7i
    faq_yzx: app-etHEVWAwR5CsLIXJISKghKmg