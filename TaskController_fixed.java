@PostMapping("/save")
public WebResultExt<UserTask> saveTask(@RequestBody UserTask template) {
    try {
        // 设置用户ID
        template.setUserId("0");
        
        // id为空则为新增
        boolean isCreate = template.getId() == null;
        boolean needCreateCron = false;
        boolean needUpdateCron = false;
        Integer oldJobId = null;
        String oldCornExp = null;
        
        // 检查是否有有效的cron表达式
        boolean hasValidCron = StrUtil.isNotBlank(template.getCornExp());
        
        if (!isCreate) {
            // 更新场景：获取原始数据
            UserTask existingTask = userTaskService.getById(template.getId());
            if (existingTask != null) {
                oldJobId = existingTask.getJobId();
                oldCornExp = existingTask.getCornExp();
                
                if (hasValidCron) {
                    // 检查三种需要创建cron任务的场景：
                    // 1. 原来没有jobId（jobId为null或0）
                    // 2. cron表达式发生了变化
                    if (oldJobId == null || oldJobId == 0) {
                        needCreateCron = true;
                    } else if (!StrUtil.equalsIgnoreCase(oldCornExp, template.getCornExp())) {
                        needUpdateCron = true;
                    }
                } else {
                    // 新的cron表达式为空，如果原来有定时任务，需要删除
                    if (oldJobId != null && oldJobId > 0) {
                        jobService.delJob(oldJobId);
                        template.setJobId(null);
                    }
                }
            }
        } else {
            // 创建场景：如果有有效的cron表达式，需要创建定时任务
            needCreateCron = hasValidCron;
        }
        
        // 先保存或更新任务基本信息
        boolean success = userTaskService.saveOrUpdate(template);
        if (!success) {
            return WebResultExt.failure("保存任务失败");
        }
        
        // 处理定时任务
        if (needCreateCron) {
            // 创建新的cron任务
            try {
                String jobId = jobService.createCronTask(template.getCornExp(), template.getId(), template.getTitle());
                template.setJobId(Integer.parseInt(jobId));
                // 更新jobId到数据库
                userTaskService.saveOrUpdate(template);
                log.info("为任务 {} 创建了新的定时任务，jobId: {}", template.getId(), jobId);
            } catch (Exception e) {
                log.error("创建定时任务失败，任务ID: {}", template.getId(), e);
                // 这里可以选择是否要回滚任务保存，根据业务需求决定
            }
        } else if (needUpdateCron) {
            // 更新现有cron任务
            try {
                jobService.updateCronTask(oldJobId, template.getCornExp());
                log.info("更新任务 {} 的定时任务，jobId: {}，新cron: {}", template.getId(), oldJobId, template.getCornExp());
            } catch (Exception e) {
                log.error("更新定时任务失败，任务ID: {}，jobId: {}", template.getId(), oldJobId, e);
            }
        }
        
        return new WebResultExt<>(template);
        
    } catch (Exception e) {
        log.error("保存任务失败", e);
        return WebResultExt.failure("保存任务失败: " + e.getMessage());
    }
}
