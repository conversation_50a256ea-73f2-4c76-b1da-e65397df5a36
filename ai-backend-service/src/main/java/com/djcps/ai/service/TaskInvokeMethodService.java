package com.djcps.ai.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.djcps.ai.common.vo.PageResult;
import com.djcps.ai.dao.dto.TaskInvokeMethodPageDto;
import com.djcps.ai.dao.entity.TaskInvokeMethod;
import com.djcps.ai.dao.mapper.TaskInvokeMethodMapper;
import com.djcps.ai.service.invokeMethod.InvokeParam;
import com.djcps.ai.service.invokeMethod.InvokeService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 方法注册服务类
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TaskInvokeMethodService extends ServiceImpl<TaskInvokeMethodMapper, TaskInvokeMethod> {
    private final Map<String, InvokeService> invokeServiceMap;
    public List<Object> fetchScopeParamList(Long taskId, Long paramUrl, String batchNo) {
        InvokeParam invokeParam = new InvokeParam();
        invokeParam.setSkey("all");
        invokeParam.setTaskId(taskId);
        invokeParam.setBatchNo(batchNo);
        return invokeById(paramUrl, invokeParam);
    }
    public List<Object> invokeMethod(Long taskId, TaskInvokeMethod method, String batchNo, String skey, JSONObject bizParam) {
        InvokeParam invokeParam = new InvokeParam();
        invokeParam.setSkey(skey);
        invokeParam.setTaskId(taskId);
        invokeParam.setBatchNo(batchNo);
        if (bizParam != null) {
            invokeParam.getMergeParams().putAll(bizParam);
        }
        return invokeByMethod(method, invokeParam);
    }
    public String invokeAnalysisMethod(Long taskId,String batch,String skey,TaskInvokeMethod analysisMethod,String prompt,String data,String methodOutputScheme) {
        if (analysisMethod == null) {
            log.error("system method not found");
            return null;
        }
        InvokeParam invokeParam = new InvokeParam();
        invokeParam.setTaskId(taskId);
        invokeParam.setSkey(skey);
        invokeParam.setBatchNo(batch);
        invokeParam.getMergeParams().put("template_prompt", prompt);
        invokeParam.getMergeParams().put("origin_data", data);
        invokeParam.getMergeParams().put("output_scheme", methodOutputScheme);
        List list = invokeByMethod(analysisMethod, invokeParam);
        if (CollUtil.isNotEmpty(list)) {
            Object first = CollUtil.getFirst(list);
            return StrUtil.toString(first);
        }
        return null;
    }

    public TaskInvokeMethod getAnalysisMethod(String bizType) {
        return getByName("模板提示词-数据-进行分析生成-"+bizType, null);
    }
    public List<Object> invokeById(@NotNull Long methodId, InvokeParam invokeParam) {
        TaskInvokeMethod method = getById(methodId);
        if (method == null) {
            return List.of();
        }
        return invokeByMethod(method,invokeParam);
    }
    public List<Object> invokeByMethod (@NotNull TaskInvokeMethod invokeMethod,InvokeParam invokeParam) {
        List<Object> result = CollUtil.newArrayList();
        InvokeService invokeService = invokeServiceMap.get(invokeMethod.getInvokeType());
        if (invokeService == null) {
            log.info("当前  invokeMethod :{} 的 invokeService type: {} 未找到", invokeMethod, invokeMethod.getInvokeType());
            return result;
        }
        if (invokeParam == null) {
            invokeParam = new InvokeParam();
        }
        invokeParam.setConfig(invokeMethod.getInvokeParam());
        Object invokeResponse = invokeService.invoke(invokeParam);
//        log.debug("invokeMethod={},invokeParam={},invokeResponse={}", invokeMethod,invokeParam, invokeResponse);
        if (invokeResponse == null) {
            return result;
        }
        List<Object> invokeResponseList = new ArrayList<>();
        if (invokeResponse instanceof List) {
            invokeResponseList = (List<Object>) invokeResponse;
        } else {
            invokeResponseList.add(invokeResponse);
        }
        return invokeResponseList;
    }
    /**
     * 分页查询方法注册列表
     *
     * @param pageDto 分页查询参数
     * @return 分页结果
     */
    public PageResult<TaskInvokeMethod> getPageList(TaskInvokeMethodPageDto pageDto) {
        // 构建查询条件
        LambdaQueryWrapper<TaskInvokeMethod> queryWrapper = new LambdaQueryWrapper<>();
        
        // 方法名称模糊查询
        if (StrUtil.isNotBlank(pageDto.getName())) {
            queryWrapper.like(TaskInvokeMethod::getName, pageDto.getName());
        }
        
        // 调用类型精确查询
        if (StrUtil.isNotBlank(pageDto.getInvokeType())) {
            queryWrapper.eq(TaskInvokeMethod::getInvokeType, pageDto.getInvokeType());
        }

        // 使用类型精确查询
        if (StrUtil.isNotBlank(pageDto.getUseType())) {
            queryWrapper.eq(TaskInvokeMethod::getUseType, pageDto.getUseType());
        }

        // 按创建时间倒序排列
        queryWrapper.orderByDesc(TaskInvokeMethod::getCreateTime);
        
        // 执行分页查询
        Page<TaskInvokeMethod> page = new Page<>(pageDto.getPageNum(), pageDto.getPageSize());
        IPage<TaskInvokeMethod> result = this.page(page, queryWrapper);
        
        return new PageResult<>(result.getTotal(), result.getRecords());
    }

    /**
     * 根据名称查询方法注册（用于重名校验）
     *
     * @param name 方法名称
     * @param excludeId 排除的ID（用于编辑时排除自身）
     * @return 方法注册对象
     */
    public TaskInvokeMethod getByName(String name, Long excludeId) {
        LambdaQueryWrapper<TaskInvokeMethod> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskInvokeMethod::getName, name);
        
        if (excludeId != null) {
            queryWrapper.ne(TaskInvokeMethod::getId, excludeId);
        }
        
        return this.getOne(queryWrapper);
    }

    /**
     * 保存或更新方法注册
     *
     * @param taskInvokeMethod 方法注册对象
     * @return 是否成功
     */
    public boolean saveOrUpdateMethod(TaskInvokeMethod taskInvokeMethod) {
        return this.saveOrUpdate(taskInvokeMethod);
    }
}
