package com.djcps.ai.core.constants;

import cn.hutool.core.util.StrUtil;

import java.time.format.DateTimeFormatter;
import java.util.regex.Pattern;

public class CommonConstants {
    /**
     * 执行SQL默认最大值
     */
    public static int RUN_SQL_LIMIT = 50;
    /**
     * limit正则,用于匹配limit N
     */
    public static final Pattern LIMIT_PATTERN = Pattern.compile("\\s+(?i)LIMIT\\s+(\\d+)\\s*$");
    //    header
    public static final String HEADER_AI_FUNCTION = "aiFunction";
    public static final String HEADER_USER_ID = "fuserid";
    public static final String HEADER_APP_NAME = "source";
    public static final String HEADER_AUTHORIZATION_BEAR = "Bearer {}";
    public static String buildHeaderAuthorizationBear(String token) {
        return StrUtil.format(HEADER_AUTHORIZATION_BEAR, token);
    }

    public static final DateTimeFormatter FULL_DATE_TIME = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter FULL_DATE = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public static final String SYSTEM_CONFIG_KEY_SQL_EMPTY_MESSAGE = "SQL_EMPTY_MESSAGE";
    //开启的功能列表
    public static final String SYSTEM_CONFIG_KEY_ENABLE_FUNCTIONS_SOURCE = "ENABLE_FUNCTIONS_{}";
    public static final String SYSTEM_CONFIG_KEY_ENABLE_FUNCTIONS_AI_REPORT = "ai_report";
    public static final String SYSTEM_CONFIG_PROMPT_GENE_CORN = "prompt_gene_corn";
    /**
     * 开启ai报表功能的用户白名单
     */
    public static final String SYSTEM_CONFIG_KEY_WHITE_LIST_AI_REPORT = "WHITE_LIST_AI_REPORT";
    public static final String SQL_EMPTY_MESSAGE_DEFAULT = "更多内容正在迭代中,敬请期待";

    public static final String SYSTEM_CONFIG_KEY_DATA_EMPTY_MESSAGE = "DATA_EMPTY_MESSAGE";
    public static final String DATA_EMPTY_MESSAGE_DEFAULT = "没有查询到数据";

}
